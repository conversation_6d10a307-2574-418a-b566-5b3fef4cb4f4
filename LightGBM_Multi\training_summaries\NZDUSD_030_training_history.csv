timestamp,scenario,architecture,train_buy_count,train_buy_win_rate,train_buy_expectancy,train_sell_count,train_sell_win_rate,train_sell_expectancy,train_total_count,train_total_win_rate,train_total_expectancy,test_buy_count,test_buy_win_rate,test_buy_expectancy,test_sell_count,test_sell_win_rate,test_sell_expectancy,test_total_count,test_total_win_rate,test_total_expectancy,accuracy,auc,f1,precision,recall,threshold,nbars_sl,num_features,hyperparameter_tuning,use_smote,performance_score
2025-07-22 13:24:07,trend_following,multi_model,75,62.67,4.44,16,56.25,5.98,91,61.54,4.71,75,62.67,4.44,16,56.25,5.98,91,61.54,4.71,0.3406593406593406,0.4697076565948118,0.0,0.0,0.0,0.5,5,216,False,False,70.66784248215761
2025-07-22 13:24:07,counter_trend,multi_model,81,69.14,8.48,10,60.0,-0.9,91,68.13,7.45,81,69.14,8.48,10,60.0,-0.9,91,68.13,7.45,0.37362637362637363,0.45586214217352383,0.0,0.0,0.0,0.5,5,216,False,False,73.66206897633847
